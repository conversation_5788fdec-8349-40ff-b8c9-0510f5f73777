---
title: 'White-labeling'
description: 'Customize the authentication experience for your brand'
---

## Overview

White-labeling allows parent accounts to customize their authentication experience. Child accounts automatically inherit the parent's branding settings.

## Organization Details

Parent accounts can set:
- Organization name
- Organization URL


### Update Organization Settings
```javascript
// Using the Authiqa API
fetch('https://api.authiqa.com/auth/update-organization', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Public-Key': 'YOUR_PUBLIC_KEY'
  },
  body: JSON.stringify({
    organizationName: 'Your Company',
    organizationUrl: 'https://yourcompany.com'
  })
});
```

<Note>
Child accounts inherit the parent's organization details.
</Note>

## Widget Customization

### Theme Options
```javascript
const widget = new AuthiqaWidget({
  publicKey: 'YOUR_PUBLIC_KEY',
  container: 'authiqa',
  theme: 'light', // or 'dark'
  mode: 'popup'   // or 'redirect'
});
```


## Email Templates

Authiqa automatically brands these transactional emails:
- Verification emails
- Password reset notifications
- Account status updates

Each email includes your:
- Organization name
- Organization URL

<Note>
Child accounts' emails use their parent organization's branding.
</Note>

## Implementation Example

```html
<!DOCTYPE html>
<html>
<head>
  <title>Login - Your Company</title>
</head>
<body>
  <div id="authiqa"></div>
  <script
    src="https://widget.authiqa.com"
    defer
    data-public-key="YOUR_PUBLIC_KEY"
    action="signin"
  ></script>
</body>
</html>
```

## Best Practices

<CardGroup cols={2}>
  <Card title="Organization Setup" icon="building">
    - Set organization details early
    - Use consistent branding
    - Consider child account experience
  </Card>
  <Card title="Widget Customization" icon="paint-brush">
    - Choose appropriate theme
    - Use consistent mode
  </Card>
</CardGroup>

## Limitations

- Child accounts cannot override parent branding
- Organization URLs must be valid HTTPS URLs
