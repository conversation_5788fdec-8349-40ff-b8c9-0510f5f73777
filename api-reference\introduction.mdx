---
title: 'API Reference'
description: 'Complete API documentation for Authiqa authentication system'
---

## Overview


Authiqa provides a RESTful API for authentication and user management. All API requests are made to endpoints beginning with: `https://api.authiqa.com/auth/`

## Authentication

All API endpoints require authentication using either:
- PUBLIC Key (for widget integrations)
- JWT <PERSON> (for authenticated requests)



Add your public key to requests using the `X-Public-Key` header:

```bash
curl -H "X-Public-Key: YOUR_PUBLIC_KEY" https://api.authiqa.com/auth/signup
```

## Request Format

All requests should be made with Content-Type: `application/json`

## Response Format

All API responses come in standard format:

```json
{
  "success": true,
  "data": {
    // Response data
  }
}
```

Or for errors:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message"
  }
}
```

## Common Error Codes

<ResponseField name="400" type="Bad Request">
  - `MISSING_REQUEST_BODY` - Request body is required
  - `INVALID_REQUEST_BODY` - Invalid JSON in request body
  - `MISSING_REQUIRED_FIELDS` - Required fields are missing
  - `INVALID_PUBLIC_KEY_FORMAT` - Public key format is invalid
</ResponseField>

<ResponseField name="401" type="Unauthorized">
  - `INVALID_PUBLIC_KEY` - public key is invalid or expired
  - `INVALID_TOKEN` - JWT token is invalid or expired
</ResponseField>

<ResponseField name="403" type="Forbidden">
  - `INSUFFICIENT_BALANCE` - Account has insufficient balance
  - `EMAIL_NOT_VERIFIED` - Email verification required
  - `ACCOUNT_INACTIVE` - Account is not active
</ResponseField>

## Rate Limiting

API requests are limited to:
- Sign in: 5 attempts per minute
- Password reset: 1 request per minute
- Email verification resend: 1 request per minute

<Card title="Need Help?" icon="question-circle">
  For additional support, contact <NAME_EMAIL>
</Card>
