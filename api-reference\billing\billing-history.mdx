---
title: 'Billing History'
description: 'Retrieve historical billing records for parent accounts'
api: 'GET https://api.authiqa.com/parent/billing-history'
---


## Overview

This endpoint allows parent accounts to retrieve their billing history with filtering capabilities by date range and pagination support.

## API Details

### Authentication

<ParamField header="Authorization" type="string" required>
  Bearer YOUR_JWT_TOKEN
</ParamField>

### Query Parameters

<ParamField query="startDate" type="string">
  Start date in YYYY-MM format
</ParamField>

<ParamField query="endDate" type="string">
  End date in YYYY-MM format
</ParamField>

<ParamField query="limit" type="number" default="10">
  Number of records (max: 100)
</ParamField>

## Try It Out

<Note>
Test the API by providing your parameters:
</Note>

<Card>
  <ParamField path="startDate" placeholder="2024-01">
    Start date (YYYY-MM)
  </ParamField>

  <ParamField path="endDate" placeholder="2024-12">
    End date (YYYY-MM)
  </ParamField>

  <ParamField path="limit" placeholder="10">
    Records per page
  </ParamField>

  <Expandable title="Headers" defaultOpen>
    <ParamField header="Authorization" required placeholder="Bearer YOUR_JWT_TOKEN" />
    <ParamField header="Content-Type" value="application/json" />
  </Expandable>

  <Button
    onClick={() => {
      alert('Sending request...');
    }}
  >
    Send Request
  </Button>
</Card>

## Code Examples

<CodeGroup>
```bash cURL
curl -X GET \
  'https://api.authiqa.com/parent/billing-history?startDate=2024-01&endDate=2024-12' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

```javascript JavaScript
const response = await fetch(
  'https://api.authiqa.com/parent/billing-history?startDate=2024-01&endDate=2024-12',
  {
    headers: {
      'Authorization': 'Bearer YOUR_JWT_TOKEN'
    }
  }
);
```

```python Python
import requests

response = requests.get(
    'https://api.authiqa.com/parent/billing-history',
    params={
        'startDate': '2024-01',
        'endDate': '2024-12'
    },
    headers={
        'Authorization': 'Bearer YOUR_JWT_TOKEN'
    }
)
```

```php PHP
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.authiqa.com/parent/billing-history?startDate=2024-01&endDate=2024-12');
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Authorization: Bearer YOUR_JWT_TOKEN'
));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);
```
</CodeGroup>

## Response Examples

<ResponseField name="200: Success">
```json
{
  "success": true,
  "data": {
    "billingRecords": [
      {
        "monthYear": "2024-01",
        "totalAccounts": 5,
        "costAssociatedWithAccounts": 0.15,
        "totalIOInteractions": 1000,
        "costAssociatedWithIO": 0.25,
        "totalFinalCost": 0.40,
        "timestamp": *************
      }
    ],
    "summary": {
      "totalCost": 0.40,
      "averageMonthlySpend": 0.40
    }
  }
}
```
</ResponseField>

<ResponseField name="400: Invalid Date Format">
```json
{
  "success": false,
  "error": {
    "code": "INVALID_DATE_FORMAT",
    "message": "Start date must be in YYYY-MM format"
  }
}
```
</ResponseField>

<ResponseField name="401: Unauthorized">
```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Only parent accounts can access billing history"
  }
}
```
</ResponseField>

## Error Codes

<ResponseField name="400 Bad Request">
  - `INVALID_DATE_FORMAT` - Date must be in YYYY-MM format
  - `INVALID_DATE_RANGE` - End date cannot be before start date
  - `INVALID_LIMIT` - Limit must be between 1 and 100
</ResponseField>

<ResponseField name="401 Unauthorized">
  - `UNAUTHORIZED` - Missing/invalid token
  - `UNAUTHORIZED_ACCESS` - Only parent accounts can access
</ResponseField>

## Usage Notes

<Card title="Date Range" icon="calendar">
  - Dates must be in YYYY-MM format
  - Maximum range: 12 months
  - Default: Current month if no dates provided
</Card>

<Card title="Pagination" icon="list-ol">
  - Default limit: 10 records
  - Maximum limit: 100 records
  - Records sorted by date (newest first)
</Card>

## Cost Components

<CardGroup cols={2}>
  <Card title="Account Costs" icon="users">
    - Based on number of child accounts
    - Prorated for partial months
    - Includes account storage
  </Card>
  <Card title="Operation Costs" icon="arrows-rotate">
    - I/O interactions
    - API calls
    - Storage operations
  </Card>
</CardGroup>