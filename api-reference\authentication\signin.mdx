---
title: 'Sign In'
description: 'Authenticate users and get access tokens'
api: 'POST https://api.authiqa.com/auth/signin'
---

## Overview

Authenticate users and receive JWT tokens for both parent and child accounts.

## API Details

### Request Format

<ParamField body="email" type="string" required>
  Email address for authentication
</ParamField>

<ParamField body="password" type="string" required>
  User's password
</ParamField>

<ParamField body="parentPublicKey" type="string">
  Required only for child accounts
</ParamField>

<RequestExample>
```json Parent Account
{
  "email": "<EMAIL>",
  "password": "Password123!"
}
```

```json Child Account
{
  "email": "<EMAIL>",
  "password": "Password123!",
  "parentPublicKey": "APK_1234567890abcdef_1234"
}
```
</RequestExample>

## Try It Out

<Note>
Test the API by filling in the values and clicking Send:
</Note>

<Card>
  <ParamField path="email" placeholder="<EMAIL>" required>
    Email address
  </ParamField>

  <ParamField path="password" type="password" placeholder="Password123!" required>
    Password
  </ParamField>

  <ParamField path="parentPublicKey" placeholder="APK_1234567890abcdef_1234">
    Parent public Key (required for child accounts)
  </ParamField>

  <Expandable title="Headers" defaultOpen>
    <ParamField header="Content-Type" value="application/json" />
  </Expandable>

  <Button
    onClick={() => {
      alert('Sending request...');
    }}
  >
    Send Request
  </Button>
</Card>

## Code Examples

<CodeGroup>
```bash cURL
curl -X POST https://api.authiqa.com/auth/signin \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123!"
  }'
```

```javascript JavaScript 
const response = await fetch('https://api.authiqa.com/auth/signin', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'Password123!'
  })
});
```

```python Python
import requests

response = requests.post(
    'https://api.authiqa.com/auth/signin',
    json={
        'email': '<EMAIL>',
        'password': 'Password123!'
    }
)
```

```php PHP
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.authiqa.com/auth/signin');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'email' => '<EMAIL>',
    'password' => 'Password123!'
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);
```
</CodeGroup>

## Response Examples

<ResponseField name="200: Success">
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "userID": "user_123",
      "email": "<EMAIL>",
      "username": "user123",
      "accountType": "parent",
      "parentAccount": "ROOT",
      "publicKey": "APK_xxx"
    }
  }
}
```
</ResponseField>


<ResponseField name="400: Invalid Request">
```json
{
  "success": false,
  "error": {
    "code": "MISSING_REQUIRED_FIELDS",
    "message": "Email and password are required"
  }
}
```
</ResponseField>

<ResponseField name="401: Invalid Credentials">
```json
{
  "success": false,
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "Invalid email or password"
  }
}
```
</ResponseField>

## Error Codes

<ResponseField name="400 Bad Request">
  - `MISSING_REQUEST_BODY` - Request body required
  - `INVALID_REQUEST_BODY` - Invalid JSON format
  - `MISSING_REQUIRED_FIELDS` - Missing email/password
  - `MISSING_PARENT_PUBLIC_KEY` - Parent public key required for child accounts
</ResponseField>

<ResponseField name="401 Unauthorized">
  - `INVALID_CREDENTIALS` - Invalid email or password
  - `INVALID_PARENT_PUBLIC_KEY` - Invalid parent public key
</ResponseField>

<ResponseField name="403 Forbidden">
  - `EMAIL_NOT_VERIFIED` - Email verification required
  - `ACCOUNT_INACTIVE` - Account is not active
  - `ACCOUNT_LOCKED` - Too many failed attempts
</ResponseField>