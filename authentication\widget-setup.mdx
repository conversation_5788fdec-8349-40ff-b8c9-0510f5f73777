---
title: 'Widget Setup'
description: 'Learn how you can integrate and configure the Authiqa authentication widget'
---


### Widget Integration


<CodeGroup>


```html HTML
<div id="authiqa"></div>

<script 
    defer

    src="https://widget.authiqa.com"
    data-public-key="YOUR_PUBLIC_KEY"
    action="signin"
></script>
```

</CodeGroup>

## Configuration Options

The widget supports these configuration attributes:

### Required Attributes
- `data-public-key`: Your PUBLIC key
- `action`: Authentication action (`signin`, `signup`, `reset`, `update`, `verify`, `resend`)
- `defer`: For proper script loading

### Optional Attributes
- `theme`: `"light"` (default) or `"dark"`
- `disable-styles`: `"true"` or `"false"` (default)
- `termsAndConditions`: URL for terms and conditions page
- `privacy`: URL for privacy policy
- `notificationSettings`: URL for notification settings
- `buttonName`: Custom text for submit button
- `data-messages`: JSON string containing custom success and loading messages
- `successAuthPath`: Custom URL to redirect after successful sign in
- `signinAuthPath`: Custom URL to redirect after email verification
- `verifyAuthPath`: Custom URL for email verification page
- `updatePasswordPath`: Custom URL for password update page
- `resendAuthPath`: Custom URL for resend verification page
- `resetAuthPath`: Custom URL for password reset page
- `data-customization`: JSON string containing layout and styling customizations
- `action`: Authentication action (optional if URL contains action)

### URL-Based Action Detection

The widget can automatically detect the authentication action from the URL if no `action` attribute is provided in the script tag. It will scan the URL for any of the valid actions (`signin`, `signup`, `verify`, `reset`, `update`, `resend`).

```html
<!-- No action attribute needed if URL contains the action -->
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_PUBLIC_KEY"
></script>
```

For example:
- If the URL is `https://example.com/auth/signup`, the widget will show the signup form
- If the URL is `https://example.com/verify?token=abc123`, the widget will show the verification form
- If the URL is `https://example.com/auth#reset`, the widget will show the password reset form

<Note>
If both the URL contains an action and the script tag has an `action` attribute, the attribute in the script tag takes precedence.
</Note>

### Direct CSS Styling

In addition to using the `data-customization` attribute, you can directly style the widget elements using CSS selectors. The widget uses specific class names that you can target in your stylesheets:

```html
<head>
    <style>
        /* Container styling */
        .authiqa-container {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
            border: none !important;
        }
        
        /* Title styling */
        .authiqa-container h1,
        #authiqa h1 {
            font-family: 'Georgia', serif !important;
            letter-spacing: 2px !important;
            color: #333 !important;
        }
        
        /* Input styling */
        .authiqa-container .authiqa-input,
        input.authiqa-input {
            border: 2px solid #6200ea !important;
            border-radius: 25px !important;
            padding: 16px !important;
        }
        
        /* Button styling */
        .authiqa-container .authiqa-button,
        button.authiqa-button {
            text-transform: uppercase !important;
            font-weight: bold !important;
            letter-spacing: 1px !important;
        }
        
        /* Terms container styling */
        .authiqa-container .terms-container {
            margin-bottom: 2rem !important; /* Increased spacing before button */
        }
    </style>
</head>
```

### CSS Class Reference

The widget uses the following CSS classes that you can target for custom styling:

| CSS Class | Element | Description |
|-----------|---------|-------------|
| `.authiqa-container` | Main container | The outer container of the widget |
| `.authiqa-title` | Title | The main heading (h1) of the form |
| `.authiqa-input` | Input fields | All input fields (email, password, etc.) |
| `.authiqa-button` | Submit button | The main action button |
| `.authiqa-password-container` | Password field wrapper | Container for password input and toggle |
| `.password-toggle` | Password visibility toggle | Button to show/hide password |
| `.authiqa-error` | Error message | Container for error messages |
| `.authiqa-success` | Success message | Container for success messages |
| `.authiqa-form` | Form element | The form element containing inputs |
| `.authiqa-label` | Input labels | Labels above input fields |
| `.authiqa-labeled-input` | Label+input container | Container for label and input pairs |
| `.terms-container` | Terms checkbox area | Container for terms and conditions checkbox and text |

<Note>
When styling the widget with CSS, use the `!important` flag to ensure your styles override the default styles. For more complex customizations, you can use the `disable-styles="true"` attribute and apply your own styles from scratch.
</Note>

### Combined Example with Custom CSS and URL-Based Action

This example shows how to combine custom CSS styling with URL-based action detection:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Custom Styled Authiqa Widget</title>
    <style>
        /* Custom styling for the widget */
        .authiqa-container {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
            border-radius: 16px !important;
        }
        
        /* Input styling */
        .authiqa-container .authiqa-input {
            border: 2px solid #6200ea !important;
            border-radius: 25px !important;
        }
        
        /* Button styling */
        .authiqa-container .authiqa-button {
            text-transform: uppercase !important;
            font-weight: bold !important;
            background-color: #6200ea !important;
        }
    </style>
</head>
<body>
    <div id="authiqa"></div>
    <script
        src="https://widget.authiqa.com"
        defer
        data-public-key="YOUR_PUBLIC_KEY"
        data-customization='{
            "colors": {
                "buttonText": "#ffffff"
            }
        }'
    ></script>
</body>
</html>
```

### Advanced Customization

The widget supports extensive customization through the `data-customization` attribute:

#### Page and Form Layout
```javascript
{
    "pageLayout": {
        "backgroundColor": "#f5f5f5",
        "formPosition": "center",
        "formMarginTop": "50px",
        "formMarginBottom": "50px"
    },
    "layout": {
        "padding": "1.5rem",
        "margin": "1rem",
        "borderRadius": "8px",
        "maxWidth": "350px"
    }
}
```

#### Colors and Typography
```javascript
{
    "colors": {
        "background": "#ffffff",
        "buttonBackground": "#007bff",
        "buttonText": "#ffffff",
        "inputBackground": "#f8f9fa",
        "inputText": "#333333",
        "inputPlaceholder": "#a3a3a3",
        "borderColor": "#dee2e6"
    },
    "typography": {
        "titleText": {
            "signinText": "Welcome Back",
            "signupText": "Create Account",
            "resetText": "Reset Password",
            "updateText": "Update Password",
            "verifyText": "Verify Email",
            "resendText": "Resend Confirmation"
        },
        "titleSize": "1.8rem",
        "titleColor": "#333333",
        "fontFamily": "Arial, sans-serif",
        "termsText": {
            "agreePrefix": "I agree with the",
            "andConnector": "and",
            "defaultPrefix": "default",
            "linkText": {
                "terms": "Terms of Service",
                "privacy": "Privacy Policy",
                "notifications": "Notification Settings"
            },
            "textColor": "#333333",
            "linkColor": "#007bff"
        }
    }
}
```

#### Input Fields and Buttons
```javascript
{
    "inputs": {
        // Placeholder customization
        "emailPlaceholder": "Enter your email",
        "passwordPlaceholder": "Enter your password",
        "usernamePlaceholder": "Choose a username",
        "confirmPasswordPlaceholder": "Confirm your password",

        // Label text customization
        "emailLabel": "Email Address",
        "passwordLabel": "Password",
        "usernameLabel": "Username",
        "confirmPasswordLabel": "Confirm Password",

        // Input field styling
        "height": "48px",
        "width": "100%",
        "padding": "12px 16px",
        "margin": "8px 0",
        "fontSize": "16px",
        "fontWeight": "400",
        "borderRadius": "8px",
        "focusBorderColor": "#10D5C6",
        "focusBoxShadow": "0 0 0 2px rgba(16, 213, 198, 0.25)",
        "placeholderAlign": "left"
    },
    "buttons": {
        "signinText": "Sign In",
        "signupText": "Sign Up",
        "resetText": "Reset Password",
        "updateText": "Update Password",
        "verifyText": "Verify Email",
        "resendText": "Resend Confirmation",
        "height": "45px",
        "width": "100%",
        "borderRadius": "25px"
    }
}
```

<Note>
Each authentication action (signin, signup, reset, update, verify, resend) has corresponding customizable text for both its title and button. This allows for complete white-labeling of all authentication forms.

All labels are properly associated with their input fields using the `for` attribute for improved accessibility. This allows users to click on labels to focus the corresponding input field and improves screen reader compatibility.
</Note>

### Input Field Customization Options

The widget now supports extensive customization of input fields through the `customization.inputs` object:

| Property | Type | Description | Example |
|----------|------|-------------|---------|
| `emailPlaceholder` | string | Placeholder text for email input | "Your work email" |
| `passwordPlaceholder` | string | Placeholder text for password input | "Create a strong password" |
| `usernamePlaceholder` | string | Placeholder text for username input | "Choose a username" |
| `confirmPasswordPlaceholder` | string | Placeholder text for confirm password input | "Re-enter your password" |
| `emailLabel` | string | Label text for email input | "Email Address" |
| `passwordLabel` | string | Label text for password input | "Password" |
| `usernameLabel` | string | Label text for username input | "Username" |
| `confirmPasswordLabel` | string | Label text for confirm password input | "Confirm Password" |
| `height` | string | Height of input fields | "48px" |
| `width` | string | Width of input fields | "100%" |
| `padding` | string | Padding inside input fields | "12px 16px" |
| `margin` | string | Margin around input fields | "8px 0" |
| `fontSize` | string | Font size of input text | "16px" |
| `fontWeight` | string | Font weight of input text | "400" |
| `borderRadius` | string | Border radius of input fields | "8px" |
| `focusBorderColor` | string | Border color when input is focused | "#10D5C6" |
| `focusBoxShadow` | string | Box shadow when input is focused | "0 0 0 2px rgba(16, 213, 198, 0.25)" |
| `placeholderAlign` | string | Text alignment for placeholder text | "left", "center", "right" |

### Complete Customization Example

```html
<script
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_PUBLIC_KEY"
    action="signup"
    data-customization='{
        "pageLayout": {
            "backgroundColor": "#f5f5f5",
            "formPosition": "center"
        },
        "layout": {
            "padding": "2rem",
            "borderRadius": "10px"
        },
        "colors": {
            "background": "#ffffff",
            "buttonBackground": "#4CAF50",
            "buttonText": "#ffffff",
            "inputPlaceholder": "#a3a3a3"
        },
        "typography": {
            "titleText": {
                "signupText": "Join Our Community"
            },
            "titleSize": "2rem",
            "termsText": {
                "textColor": "#555555",
                "linkColor": "#4CAF50"
            }
        },
        "inputs": {
            "emailPlaceholder": "Your email address",
            "emailLabel": "Work Email",
            "passwordPlaceholder": "Create a strong password",
            "passwordLabel": "Create Password",
            "usernameLabel": "Choose Username",
            "height": "48px",
            "borderRadius": "8px",
            "focusBorderColor": "#4CAF50"
        },
        "buttons": {
            "signupText": "Create Account"
        }
    }'
></script>
```

<Note>
Labels follow accessibility best practices by being properly associated with their input fields using the `for` attribute. This improves usability for screen readers and allows users to click on the label to focus the input field.
</Note>

## Google SSO Integration

The Authiqa widget includes built-in support for Google Single Sign-On (SSO) authentication. When Google SSO is enabled for your organization, the widget automatically displays Google authentication options without requiring additional configuration.

### Google SSO Features

<CardGroup cols={2}>
  <Card title="Google Sign-In Button" icon="google">
    - Automatically appears below the main submit button on Sign In and Sign Up forms
    - Only displayed when Google SSO is enabled for your organization
    - Styled to match your widget's theme and customization
    - Handles both sign-in and sign-up flows seamlessly
  </Card>
  <Card title="Google One Tap" icon="fingerprint">
    - Displays Google's One Tap prompt for streamlined authentication
    - Appears after the Google button is rendered (with a short delay)
    - Only shown once per session to avoid repetitive prompts
    - Provides the fastest authentication experience for returning users
  </Card>
</CardGroup>

### How Google SSO Works

1. **Automatic Detection**: The widget checks if Google SSO is enabled for your organization
2. **Dynamic Rendering**: If enabled, a Google Sign-In/Sign-Up button appears below the main form
3. **One Tap Integration**: Google One Tap prompt may appear for eligible users
4. **Error Handling**: Clear error messages are displayed if Google authentication fails
5. **Token Management**: Successful Google authentication follows the same token storage as regular authentication

### Configuration Requirements

<Note>
**No additional configuration is needed** to enable Google SSO in the widget. The feature is automatically handled based on your organization's backend settings. Simply ensure that:

1. Google SSO is enabled for your organization through the Authiqa dashboard
2. Your organization has a valid Google OAuth client ID configured
3. The widget script is properly loaded with your public key
</Note>

### Google SSO Example

```html
<div id="authiqa"></div>
<script
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_PUBLIC_KEY"
    action="signin"
></script>
<!-- Google SSO button will automatically appear if enabled -->
```

### Google Authentication Flow

The Google SSO integration supports both authentication flows:

- **Sign In**: Existing users can sign in with their Google account
- **Sign Up**: New users can create an account using their Google credentials
- **Account Linking**: Users can link their existing Authiqa account with Google

### Error Handling

If Google authentication encounters an error, the widget displays clear error messages:

- Network connectivity issues
- Invalid Google credentials
- Account linking conflicts
- Organization-specific restrictions

<Note>
Google SSO availability depends on your organization's configuration. Contact your administrator if you expect to see Google authentication options but they don't appear.
</Note>

### Custom Messages
The widget supports customizable messages for success and loading states:

```javascript
{
    // Success messages
    signinSuccess: string;
    signupSuccess: string;
    resetSuccess: string;
    updateSuccess: string;
    resendSuccess: string;
    verificationSuccess: string;
    
    // Loading messages
    signinLoading: string;
    signupLoading: string;
    resetLoading: string;
    updateLoading: string;
    resendLoading: string;
    verificationLoading: string;
}
```

<Note>
Error messages are not customizable as they display original server responses with error codes for better debugging.
</Note>

### Message Behavior

<CardGroup cols={2}>
  <Card title="Display Duration" icon="clock">
    - Success messages: 2 seconds
    - Error messages: 5 seconds
    - Loading messages: During processing
  </Card>
  <Card title="Message Types" icon="message">
    - Success messages: Green with checkmark
    - Loading messages: Below submit button
    - Error messages: Red with error code
  </Card>
</CardGroup>

<Note>
If no custom messages are provided, the widget will use default messages. You can customize either success or loading messages independently.
</Note>

### Authentication Paths

The widget supports custom redirection paths for different authentication flows. Some of these paths also affect the URLs included in automated emails:

<CardGroup cols={2}>
  <Card title="Sign In Flow" icon="arrow-right">
    After successful sign in, redirects to:
    1. Custom `successAuthPath` if provided
    2. Default success page if not provided
  </Card>
  <Card title="Email Links" icon="envelope">
    When provided, these paths are used in automated emails:
    - `verifyAuthPath`: Used in verification emails (signup and resend)
    - `updatePasswordPath`: Used in password reset emails
  </Card>
</CardGroup>

### Path Configuration Examples

```html
<!-- Signup with custom verification URL -->
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_public_KEY"
    action="signup"
    verifyAuthPath="https://your-site.com/verify"
    // The verification email will include: https://your-site.com/verify?token=ABC123
></script>

<!-- Password reset with custom update URL -->
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_public_KEY"
    action="reset"
    updatePasswordPath="https://your-site.com/update-password"
    // The reset email will include: https://your-site.com/update-password?token=XYZ789
></script>

<!-- Complete authentication flow example -->
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_public_KEY"
    action="verify"
    signinAuthPath="https://your-site.com/login"
    verifyAuthPath="https://your-site.com/verify"
></script>
```

<Note>
The `verifyAuthPath` and `updatePasswordPath` are particularly important as they determine the URLs in authentication emails:
- Verification emails (signup & resend) will use `verifyAuthPath` + verification token
- Password reset emails will use `updatePasswordPath` + reset token
</Note>

### Theme Examples

```html
<!-- Light theme (default) -->
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_public_KEY"
    action="signin"
></script>

<!-- Dark theme -->
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_public_KEY"
    action="signin"
    theme="dark"
></script>

<!-- Custom styling (disable default styles) -->
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_public_KEY"
    action="signin"
    disable-styles="true"
></script>
```

## Event Handling

```javascript
// Success events
widget.on('widget:success', (data) => {
  console.log('Authentication successful:', data);
  // Tokens are automatically stored in localStorage:
  // - authiqa_token: JWT access token
  // - authiqa_token_expiration: Token expiration
  // - authiqa_parent_jwt_secret: Parent JWT secret (for child accounts)
});

// Error events  
widget.on('widget:error', (error) => {
  console.error('Authentication failed:', error);
});

// Verification events
widget.on('verify:success', () => {
  console.log('Email verification successful');
});
```

### HTML Examples for Different Actions

<CodeGroup>

```html Sign In
<div id="authiqa"></div>
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_public_KEY"
    action="signin"
    data-messages='{
        "signinSuccess": "Welcome back! Redirecting you to dashboard...",
        "signinLoading": "Authenticating your credentials..."
    }'
></script>
```

```html Sign Up
<div id="authiqa"></div>
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_public_KEY"
    action="signup"
    termsAndConditions="https://your-site.com/terms"
    privacy="https://your-site.com/privacy"
    notificationSettings="https://your-site.com/notifications"
    buttonName="Create Account"
    data-messages='{
        "signupSuccess": "Account created successfully! Please check your email for verification.",
        "signupLoading": "Creating your account..."
    }'
    data-customization='{
        "typography": {
            "termsText": {
                "textColor": "#555555",
                "linkColor": "#4CAF50"
            }
        }
    }'
></script>
```

```html Password Reset
<div id="authiqa"></div>
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_public_KEY"
    action="reset"
></script>
```

```html Email Verify
<div id="authiqa"></div>
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_public_KEY"
    action="verify"
></script>
```

```html Password Update
<div id="authiqa"></div>
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_public_KEY"
    action="update"
></script>
```

```html Resend Verification
<div id="authiqa"></div>
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_public_KEY"
    action="resend"
></script>
```

</CodeGroup>

## React Package

For React applications, we provide a dedicated React wrapper package that simplifies integration and provides a more React-friendly API.

### Installation

```bash
npm install authiqa-react
# or
yarn add authiqa-react
```

Include the Authiqa Widget script in your HTML (typically in `index.html`):

```html
<script defer src="https://widget.authiqa.com"></script>
```

### Basic Usage

```jsx
import { AuthiqaWidget } from 'authiqa-react';

function App() {
  const handleSuccess = (data) => {
    console.log('Authentication successful:', data);
    // Redirect or update state based on successful authentication
  };

  const handleError = (error) => {
    console.error('Authentication failed:', error);
    // Handle authentication errors
  };

  return (
    <div className="app">
      <h1>My Application</h1>
      <AuthiqaWidget 
        publicKey="YOUR_public_KEY"
        action="signin"
        onSuccess={handleSuccess}
        onError={handleError}
      />
    </div>
  );
}

export default App;
```

### Critical Vite Configuration

⚠️ **Important**: For Vite applications, include this specific configuration in `vite.config.js`:

```javascript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['authiqa-react'],
  },
  build: {
    commonjsOptions: {
      include: [/authiqa-react/, /node_modules/],
    },
  },
});
```

This configuration is required for the Authiqa widget to initialize properly in Vite applications. Without it, the widget may not load or function correctly.

### Props Reference

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `publicKey` | string | Yes | - | Your Authiqa public key |
| `action` | string | No | "signin" | Authentication action ("signin", "signup", "reset", "verify", "update", "resend") |
| `theme` | string | No | "light" | Widget theme ("light", "dark") |
| `mode` | string | No | "popup" | Display mode ("popup", "inline") |
| `container` | string | Yes | "authiqa-react-container" | Container ID |
| `onSuccess` | function | No | - | Callback when authentication succeeds |
| `onError` | function | No | - | Callback when authentication fails |
| `customization` | object | No | - | Custom styling options (colors, typography, layout) |
| `termsAndConditions` | string | No | - | URL to terms and conditions |
| `privacy` | string | No | - | URL to privacy policy |
| `notificationSettings` | string | No | - | URL to notification settings |
| `verifyAuthPath` | string | No | - | Path for email verification |
| `updatePasswordPath` | string | No | - | Path for password update |
| `resendAuthPath` | string | No | - | Path for resending verification |
| `successAuthPath` | string | No | - | Path for success redirect |
| `signinAuthPath` | string | No | - | Path for sign-in redirect |

### Customization Options

The `customization` prop accepts an object with the following structure:

```jsx
<AuthiqaWidget 
  publicKey="YOUR_public_KEY"
  action="signin"
  customization={{
    pageLayout: {
      backgroundColor: "#f5f5f5",
      formPosition: "center"
    },
    layout: {
      padding: "2rem",
      borderRadius: "10px"
    },
    colors: {
      background: "#ffffff",
      buttonBackground: "#4CAF50",
      buttonText: "#ffffff"
    },
    typography: {
      titleText: {
        signinText: "Welcome Back"
      },
      titleSize: "2rem"
    },
    inputs: {
      emailPlaceholder: "Your email address",
      emailLabel: "Work Email",
      passwordPlaceholder: "Enter your password",
      passwordLabel: "Password",
      height: "48px",
      borderRadius: "8px",
      focusBorderColor: "#4CAF50",
      placeholderAlign: "center"
    },
    buttons: {
      signinText: "Log In"
    }
  }}
/>
```

### Event Handling

```jsx
import { AuthiqaWidget } from 'authiqa-react';

function AuthPage() {
  const handleSuccess = (data) => {
    console.log('Authentication successful:', data);
    // data contains:
    // - token: JWT access token
    // - user: User information
    // - expiresAt: Token expiration timestamp
    
    // Tokens are automatically stored in localStorage:
    // - authiqa_token: JWT access token
    // - authiqa_token_expiration: Token expiration
  };

  const handleError = (error) => {
    console.error('Authentication failed:', error);
    // error contains:
    // - message: Error message
    // - code: Error code (if available)
  };

  return (
    <AuthiqaWidget 
      publicKey="YOUR_public_KEY"
      action="signin"
      onSuccess={handleSuccess}
      onError={handleError}
    />
  );
}
```

### Authentication Flow Examples

#### Sign In Example

```jsx
import { AuthiqaWidget } from 'authiqa-react';
import { useNavigate } from 'react-router-dom';

function SignIn() {
  const navigate = useNavigate();
  
  const handleSuccess = () => {
    navigate('/dashboard');
  };
  
  return (
    <div className="signin-page">
      <h1>Sign In</h1>
      <AuthiqaWidget 
        publicKey="YOUR_public_KEY"
        action="signin"
        onSuccess={handleSuccess}
        successAuthPath="/dashboard"
      />
    </div>
  );
}
```

#### Sign Up Example

```jsx
import { AuthiqaWidget } from 'authiqa-react';

function SignUp() {
  return (
    <div className="signup-page">
      <h1>Create Account</h1>
      <AuthiqaWidget 
        publicKey="YOUR_public_KEY"
        action="signup"
        termsAndConditions="https://your-site.com/terms"
        privacy="https://your-site.com/privacy"
        verifyAuthPath="/verify"
      />
    </div>
  );
}
```

### Framework Integration Examples

#### Next.js Example

```jsx
// components/AuthiqaSignIn.js
'use client';

import { AuthiqaWidget } from 'authiqa-react';
import { useRouter } from 'next/navigation';

export default function AuthiqaSignIn() {
  const router = useRouter();
  
  const handleSuccess = () => {
    router.push('/dashboard');
  };
  
  return (
    <AuthiqaWidget 
      publicKey={process.env.NEXT_PUBLIC_AUTHIQA_public_KEY}
      action="signin"
      onSuccess={handleSuccess}
    />
  );
}
```

#### React Router Example

```jsx
import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthiqaWidget } from 'authiqa-react';

// Authentication guard component
function RequireAuth({ children }) {
  const isAuthenticated = localStorage.getItem('authiqa_token') !== null;
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return children;
}

function App() {
  return (
    <Routes>
      <Route path="/login" element={
        <AuthiqaWidget 
          publicKey="YOUR_public_KEY"
          action="signin"
          successAuthPath="/dashboard"
        />
      } />
      <Route path="/signup" element={
        <AuthiqaWidget 
          publicKey="YOUR_public_KEY"
          action="signup"
          verifyAuthPath="/verify"
        />
      } />
      <Route path="/dashboard" element={
        <RequireAuth>
          <Dashboard />
        </RequireAuth>
      } />
    </Routes>
  );
}
```

### Troubleshooting

- **Widget not loading**: Ensure the script is included in your HTML and the container element exists
- **Vite configuration issues**: Make sure to use the exact configuration provided in the Vite Configuration section
- **Event handlers not firing**: Check that event names match expected values (`onSuccess`, `onError`)
- **Styling conflicts**: Use the `customization` prop to override default styles or apply CSS targeting the widget container

### Password Expiry Handling

The widget includes built-in support for password expiry management. By default, passwords expire after 90 days to maintain security best practices.

<CardGroup cols={2}>
  <Card title="Expired Passwords" icon="lock">
    When a password has expired (90+ days old):
    - User is redirected to the password reset page
    - A warning message is displayed
    - User must update their password to continue
  </Card>
  <Card title="Expiring Soon" icon="clock-rotate-left">
    When a password will expire soon:
    - Warning message shows days until expiry
    - For urgent warnings (≤3 days), message displays for 3 seconds
    - For less urgent warnings (4-14 days), user can proceed immediately
  </Card>
</CardGroup>

This feature helps maintain security compliance by ensuring users update their passwords regularly. The 90-day password rotation policy follows industry security standards and helps protect user accounts from unauthorized access.

The expiry warning thresholds are:
- Critical: 3 days or less (enforced delay before proceeding)
- Warning: 4-14 days (informational only)
- No warning: 15-89 days remaining

<Note>
Password expiry is managed server-side. The widget simply responds to the expiry status returned by the authentication API. The 90-day expiry period starts from the date when the password was last changed.
</Note>

## Global Configuration

The Authiqa Widget supports a centralized configuration approach that allows you to define styling and messaging options once and apply them across all authentication forms on your website.

### How It Works

1. Create a JavaScript file (e.g., `global-config.js`) with your customization settings
2. Include this file in your HTML pages before the Authiqa widget script
3. The widget will automatically apply these settings to all forms

```html
<head>
    <!-- Include the global config first -->
    <script src="/path/to/global-config.js"></script>
    <script 
        src="https://widget.authiqa.com" 
        defer 
        data-public-key="YOUR_public_KEY" 
        action="signin"
    ></script>
</head>
```

<Note>
The global configuration file can have any name you choose - it doesn't have to be named "global-config.js". What matters is that it defines the `window.AuthiqaGlobalConfig` object and is loaded before the widget script.
</Note>

### Global Configuration Structure

The global configuration uses a `window.AuthiqaGlobalConfig` object with two main sections:
- `customization`: Contains all styling and layout options
- `messages`: Contains custom success and loading messages

```javascript
// Global configuration for Authiqa Widget
window.AuthiqaGlobalConfig = {
    customization: {
        // Layout, colors, typography, etc.
    },
    messages: {
        // Custom success and loading messages
    }
};
```

### Available Global Customization Options

The global configuration supports all the same customization options available through the `data-customization` attribute, including:

#### Layout and Colors
```javascript
customization: {
    // Layout
    layout: {
        padding: "1.5rem",
        borderRadius: "8px",
        maxWidth: "400px"
    },
    
    // Colors
    colors: {
        background: "#27272a",
        buttonBackground: "#10D5C6",
        buttonText: "#1F2025",
        inputBackground: "#212125",
        inputText: "#ffffff",
        inputPlaceholder: "#71717a",
        borderColor: "#3f3f46",
        inputFocusBorderColor: "#10D5C6",
        inputFocusBoxShadow: "0 0 0 2px rgba(16, 213, 198, 0.25)",
        buttonHoverBackground: "#0fbfb1"
    },
    
    // Page Layout
    pageLayout: {
        backgroundColor: "#212125"
    }
}
```

#### Typography and Buttons
```javascript
customization: {
    // Typography
    typography: {
        titleText: {
            signinText: "Sign In",
            signupText: "Get Started",
            resetText: "Forgotten Password?",
            updateText: "Update Password",
            verifyText: "Verify Email",
            resendText: "Resend Confirmation Email"
        },
        subtitleText: {
            signinText: "Welcome back to your account",
            signupText: "Start Authenticating users in minutes",
            resetText: "We'll send you a reset link",
            updateText: "Create a new secure password",
            resendText: "Didn't receive the email?"
        },
        titleSize: "1.9rem",
        titleColor: "#10D5C6",
        titleAlignment: "left",
        titleWeight: "800",
        titleLineHeight: "1.2",
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
        termsText: {
            agreePrefix: "I agree with the",
            andConnector: "and",
            defaultPrefix: "default",
            linkText: {
                terms: "Terms of Service",
                privacy: "Privacy Policy",
                notifications: "Notification Settings"
            },
            textColor: "#ffffff",
            linkColor: "#10D5C6"
        }
    },
    
    // Buttons
    buttons: {
        height: "2.5rem",
        borderRadius: "4px"
    }
}
```

#### Advanced CSS
```javascript
customization: {
    // Advanced CSS (custom CSS that will be injected)
    advancedCSS: `
        /* Add subtitle after h1 */
        .authiqa-container h1:after {
            content: attr(data-subtitle);
            display: block;
            color: #ffffff !important;
            font-size: 0.7rem !important;
            font-weight: 400 !important;
            margin-top: 0.4rem !important;
            line-height: 0.6 !important;
            margin-bottom: 1rem !important;
        }
        
        /* Input focus state */
        .authiqa-container input:focus {
            border-color: #10D5C6 !important;
            box-shadow: 0 0 0 2px rgba(16, 213, 198, 0.25) !important;
        }
        
        /* Button hover state */
        .authiqa-container button[type="submit"]:hover {
            background-color: #0fbfb1 !important;
        }
        
        /* Title alignment */
        .authiqa-container h1 {
            text-align: left !important;
            font-weight: 800 !important;
            line-height: 1.2 !important;
        }
    `
}
```

### Global Messages

The global configuration also supports custom messages for different authentication actions:

```javascript
messages: {
    signupSuccess: "Account created successfully! Please check your email for verification.",
    signupLoading: "Creating your account...",
    signinSuccess: "Welcome back! You've successfully signed in.",
    resetSuccess: "Password reset link sent! Please check your email.",
    resetLoading: "Sending password reset link...",
    updateSuccess: "Password updated successfully! You can now sign in with your new password.",
    updateLoading: "Updating your password...",
    resendSuccess: "Confirmation email sent! Please check your inbox.",
    resendLoading: "Sending confirmation email..."
}
```

### Overriding Global Settings

You can override global settings on a per-page basis by providing customization attributes directly in the widget script tag:

```html
<script
    src="https://widget.authiqa.com"
    defer
    data-public-key="YOUR_public_KEY"
    data-customization='{
        "colors": {
            "buttonBackground": "#FF5500"
        }
    }'
></script>
```

Page-specific settings will take precedence over global settings for the same properties.

<Note>
The global configuration is particularly useful for maintaining consistent branding across all authentication forms on your website. It reduces duplication and makes it easier to update the styling of all forms at once.
</Note>

### Complete Global Configuration Example

Here's a complete example of a global configuration file:

```javascript
// global-config.js
window.AuthiqaGlobalConfig = {
    customization: {
        // Layout
        layout: {
            padding: "1.5rem",
            borderRadius: "8px",
            maxWidth: "400px"
        },
        
        // Colors
        colors: {
            background: "#27272a",
            buttonBackground: "#10D5C6",
            buttonText: "#1F2025",
            inputBackground: "#212125",
            inputText: "#ffffff",
            inputPlaceholder: "#71717a",
            borderColor: "#3f3f46",
            inputFocusBorderColor: "#10D5C6",
            inputFocusBoxShadow: "0 0 0 2px rgba(16, 213, 198, 0.25)",
            buttonHoverBackground: "#0fbfb1"
        },
        
        // Typography
        typography: {
            titleText: {
                signinText: "Sign In",
                signupText: "Get Started",
                resetText: "Forgotten Password?",
                updateText: "Update Password",
                verifyText: "Verify Email",
                resendText: "Resend Confirmation Email"
            },
            subtitleText: {
                signinText: "Welcome back to your account",
                signupText: "Start Authenticating users in minutes",
                resetText: "We'll send you a reset link",
                updateText: "Create a new secure password",
                resendText: "Didn't receive the email?"
            },
            titleSize: "1.9rem",
            titleColor: "#10D5C6",
            titleAlignment: "left",
            titleWeight: "800",
            titleLineHeight: "1.2",
            fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
            termsText: {
                agreePrefix: "I agree with the",
                andConnector: "and",
                defaultPrefix: "default",
                linkText: {
                    terms: "Terms of Service",
                    privacy: "Privacy Policy",
                    notifications: "Notification Settings"
                },
                textColor: "#ffffff",
                linkColor: "#10D5C6"
            }
        },

        // Input Fields
        inputs: {
            emailPlaceholder: "Your work email",
            passwordPlaceholder: "Create a strong password",
            usernamePlaceholder: "Choose a username",
            confirmPasswordPlaceholder: "Re-enter your password",
            emailLabel: "Email Address",
            passwordLabel: "Password",
            usernameLabel: "Username",
            confirmPasswordLabel: "Confirm Password",
            height: "48px",
            borderRadius: "8px",
            focusBorderColor: "#10D5C6",
            focusBoxShadow: "0 0 0 2px rgba(16, 213, 198, 0.25)",
            placeholderAlign: "left"
        },

        // Buttons
        buttons: {
            height: "2.5rem",
            borderRadius: "4px"
        },
        
        // Page Layout
        pageLayout: {
            backgroundColor: "#212125"
        },
        
        // Advanced CSS
        advancedCSS: `
            /* Custom CSS here */
        `
    },
    
    // Custom messages
    messages: {
        signupSuccess: "Account created successfully! Please check your email for verification.",
        signupLoading: "Creating your account...",
        signinSuccess: "Welcome back! You've successfully signed in.",
        resetSuccess: "Password reset link sent! Please check your email.",
        resetLoading: "Sending password reset link...",
        updateSuccess: "Password updated successfully! You can now sign in with your new password.",
        updateLoading: "Updating your password...",
        resendSuccess: "Confirmation email sent! Please check your inbox.",
        resendLoading: "Sending confirmation email..."
    }
};
```

### Hash-Based Navigation

The widget now supports hash-based navigation, allowing users to switch between different authentication forms by changing the URL hash.

```html
<div id="authiqa"></div>

<!-- Include the Authiqa widget -->
<script 
    defer
    src="https://widget.authiqa.com"
    data-public-key="YOUR_public_KEY"
></script>

<!-- Include the hash navigation script -->
<script src="https://widget.authiqa.com/latest/hash-navigation.js" defer></script>

<!-- Optional navigation links -->
<div class="auth-links">
    <a href="#signin">Sign In</a>
    <a href="#signup">Sign Up</a>
    <a href="#reset">Reset Password</a>
</div>
```

#### Navigation Priority

The hash navigation follows these priority rules:

1. If a URL hash is present (e.g., `#signin`), it will show the corresponding form
2. If no hash is present, it will use the `action` attribute from the script tag
3. If neither is available, it will default to the signin form

This allows you to set a default form using the `action` attribute while still enabling hash-based navigation.

#### Supported Hash Values

The hash navigation script supports the following hash values:

| Hash | Form Displayed |
|------|---------------|
| `#signin` | Sign In form |
| `#signup` | Sign Up form |
| `#verify` | Email Verification form |
| `#reset` | Password Reset form |
| `#update` | Password Update form |
| `#resend` | Resend Confirmation form |

#### How It Works

- The hash navigation script automatically detects changes to the URL hash
- When the hash changes to a valid action (e.g., `#signin`), the corresponding form is displayed
- The script works with any URL structure as long as it contains a valid hash

#### URL Examples

The hash-based navigation works with any URL structure:

- `https://example.com#signin`
- `https://example.com/auth#signup`
- `https://example.com/account/settings?user=123#reset`

<Note>
The hash navigation script must be included after the main widget script. It automatically uses the same configuration attributes (public key, theme, custom paths, etc.) from the main widget script.
</Note>

#### React Integration

For React applications, you can use the hash navigation with the React wrapper:

```jsx
import { AuthiqaWidget } from 'authiqa-react';

function App() {
  // Include the hash navigation script in your HTML
  // <script src="https://widget.authiqa.com/latest/hash-navigation.js" defer></script>
  
  return (
    <div className="app">
      <AuthiqaWidget 
        publicKey="YOUR_public_KEY"
        // No need to specify action - it will be determined by the hash
      />
      
      {/* Optional navigation links */}
      <div className="auth-links">
        <a href="#signin">Sign In</a>
        <a href="#signup">Sign Up</a>
        <a href="#reset">Reset Password</a>
      </div>
    </div>
  );
}
```

## Domain Restriction

The Authiqa widget includes a domain restriction feature that controls where the widget can be used:

<Card title="Domain Restriction" icon="shield-check">
By default, the widget is restricted to work only on the organization's domain for security purposes. This prevents unauthorized use of your authentication widget on other websites.

For testing purposes (such as on JSFiddle, CodePen, or local development environments), this restriction can be disabled through the organization settings API.

The domain restriction status can be checked via the [Get Organization Details](/api-reference/organization/organization-details) API and toggled using the [Update Organization](/api-reference/organization/organization) API.
</Card>

<Note>
When developing locally or in test environments where the domain doesn't match your organization's domain, you may need to temporarily disable domain restriction through the API.
</Note>
