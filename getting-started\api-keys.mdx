---
title: 'Credentials'
description: 'Manage Credentials for Authiqa integration'
---

public keys are required to authenticate your requests to Authiqa. Each key is unique to your organization and helps track usage.

## Generate public Key and JWT Secret

<Steps>
  <Step title="Access Dashboard">
    Log in to your [Authiqa Dashboard](https://authiqa.com/signup)
  </Step>

  <Step title="Navigate to Credentials Page">
    Click on "Credentials" in the navigation bar at the top
  </Step>

  <Step title="View Your Credentials">
    You will find two secure credentials:
    
    
    **public Key:**
    ```bash
    •••••••••••••••••••••••• (Hidden by default)
    ```
    - Show/Hide button to reveal or hide the key
    - Copy button to copy to clipboard
    
    **JWT Secret:**
    ```bash
    •••••••••••••••••••••••• (Hidden by default)
    ```
    - Show/Hide button to reveal or hide the secret
    - Copy button to copy to clipboard
    
    <Note>
    When copying either credential, a "Copied to clipboard" message will appear at the top of the page
    </Note>
  </Step>

  <Step title="Save Keys Securely">
    Copy and store both credentials securely - they won't be shown in plaintext by default
  </Step>
</Steps>

<Warning>
  Never share your public key or JWT secret or commit them to version control. Use environment variables to store them securely.
</Warning>

## public Key Usage

Add your public key when initializing the Authiqa widget:

```html
<script
  src="https://widget.authiqa.com"
  defer
  data-public-key="YOUR_PUBLIC_KEY"
  action="signin"
></script>
```


## Key Management

- **Parent Account Keys**: Used for managing child accounts and billing
- **Child Account Keys**: Limited to authentication operations only

## Security Best Practices

<Check>
  - [x] Store keys in environment variables
  - [x] Monitor key usage for unusual activity
</Check>

## Next Steps

<CardGroup cols={2}>
  <Card
    title="Install Widget"
    icon="code"
    href="/getting-started/widget-installation"
  >
    Add the authentication widget to your application
  </Card>
  <Card
    title="Security Guide"
    icon="shield-check"
    href="/features/security"
  >
    Learn about securing your integration
  </Card>
</CardGroup>