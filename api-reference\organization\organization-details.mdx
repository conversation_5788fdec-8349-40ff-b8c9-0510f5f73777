---
title: 'Get Organization Details'
description: 'Retrieve authentication URLs and organization details'
api: 'GET https://api.authiqa.com/auth/organization-details'
---


## Overview

This endpoint retrieves authentication URLs for both parent and child accounts. For child accounts, it returns the parent organization's configured URLs.

## API Details

### Authentication

<ParamField header="X-public-Key" type="string" required>
  Your public key for authentication
</ParamField>

<RequestExample>
```bash cURL
curl -X GET https://api.authiqa.com/auth/organization-details \
  -H "X-Public-Key: YOUR_PUBLIC_KEY" \
  -H "Content-Type: application/json"
```

```javascript JavaScript
const response = await fetch('https://api.authiqa.com/auth/organization-details', {
  method: 'GET',
  headers: {
    'X-Public-Key': 'YOUR_PUBLIC_KEY',
    'Content-Type': 'application/json'
  }
});
```

```python Python
import requests

response = requests.get(
    'https://api.authiqa.com/auth/organization-details',
    headers={
        'X-Public-Key': 'YOUR_PUBLIC_KEY',
        'Content-Type': 'application/json'
    }
)
```

```php PHP
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.authiqa.com/auth/organization-details');
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'X-Public-Key: YOUR_PUBLIC_KEY',
    'Content-Type: application/json'
));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);
```
</RequestExample>

## Try It Out

<Note>
Test the API by providing your public key:
</Note>

<Card>
  <ParamField path="X-Public-Key" placeholder="APK_1234567890abcdef_1234" required>
    Your public key
  </ParamField>

  <Expandable title="Headers" defaultOpen>
    <ParamField header="Content-Type" value="application/json" />
  </Expandable>

  <Button
    onClick={() => {
      alert('Sending request...');
    }}
  >
    Send Request
  </Button>
</Card>

## Process Flow

<Steps>
  <Step title="Authentication">
    - Extract public key from headers
    - Validate public key format
    - Retrieve user by public key
  </Step>

  <Step title="Account Resolution">
    - Determine account type (parent/child)
    - For child accounts, fetch parent details
    - Verify parent account status
  </Step>

  <Step title="URL Collection">
    - Gather authentication URLs
    - Validate URL accessibility
    - Format response structure
  </Step>

  <Step title="Usage Tracking">
    - Increment retrieval counter
    - Update usage statistics
  </Step>
</Steps>

## Response Examples

<ResponseField name="200: Success">
```json
{
  "success": true,
  "data": {
    "message": "Organization details retrieved successfully",
    "authUrls": {
      "signin": "https://example.com/signin",
      "signup": "https://example.com/signup",
      "verify": "https://example.com/verify",
      "reset": "https://example.com/reset",
      "update": "https://example.com/update",
      "resend": "https://example.com/resend",
      "successful": "https://example.com/success"
    },
    "domainRestrictionEnabled": true
  }
}
```
</ResponseField>

<ResponseField name="400: Missing public Key">
```json
{
  "success": false,
  "error": {
    "code": "MISSING_PARENT_PUBLIC_KEY",
    "message": "public key is required"
  }
}
```
</ResponseField>

<ResponseField name="404: User Not Found">
```json
{
  "success": false,
  "error": {
    "code": "USER_NOT_FOUND",
    "message": "User not found"
  }
}
```
</ResponseField>

## Error Codes

### Authentication Errors
<ResponseField name="400 Bad Request">
  - `MISSING_PARENT_PUBLIC_KEY` - public key not provided
  - `INVALID_PARENT_ACCOUNT` - Parent account invalid
</ResponseField>

<ResponseField name="404 Not Found">
  - `USER_NOT_FOUND` - No user found for public key
</ResponseField>

## URL Requirements

<Card title="Required URLs" icon="link">
All authentication URLs must be:
- Accessible via HTTPS
- Under organization's domain
- Properly configured for:
  - Sign in/up flows
  - Email verification
  - Password management
  - Success redirects
</Card>

## Domain Restriction

<Card title="Domain Restriction Setting" icon="shield-check">
The API response includes a `domainRestrictionEnabled` boolean field that indicates whether the authentication widget is restricted to the organization's domain:

- When `true` (default): Widget only works on the organization's domain
- When `false`: Widget works on any domain (for testing purposes)

This setting helps control where the authentication widget can be used, providing security while allowing flexibility for testing.
</Card>
