---
title: 'Update Password'
description: 'Update user password using reset token'
api: 'POST https://api.authiqa.com/auth/update-password'
---


## Overview

This endpoint allows users to update their password using a secure reset token obtained through the password reset flow. The token contains an encrypted OTP that must be valid to allow the password update.

## API Details

### Request Format

<ParamField body="token" type="string" required>
  Reset token received via email
</ParamField>

<ParamField body="password" type="string" required>
  New password meeting security requirements
</ParamField>

<ParamField body="parentPublicKey" type="string">
  Required only for child accounts
</ParamField>

<RequestExample>
```bash cURL
curl -X POST https://api.authiqa.com/auth/update-password \
  -H "Content-Type: application/json" \
  -d '{
    "token": "YOUR_RESET_TOKEN",
    "password": "NewSecureP@ss123"
  }'
```

```javascript JavaScript
const response = await fetch('https://api.authiqa.com/auth/update-password', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    token: 'YOUR_RESET_TOKEN',
    password: 'NewSecureP@ss123'
  })
});
```

```python Python
import requests

response = requests.post(
    'https://api.authiqa.com/auth/update-password',
    json={
        'token': 'YOUR_RESET_TOKEN',
        'password': 'NewSecureP@ss123'
    }
)
```

```php PHP
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.authiqa.com/auth/update-password');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'token' => 'YOUR_RESET_TOKEN',
    'password' => 'NewSecureP@ss123'
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);
```
</RequestExample>

## Try It Out

<Note>
Test the API by providing your reset token and new password:
</Note>

<Card>
  <ParamField path="token" placeholder="YOUR_RESET_TOKEN" required>
    Reset token from email
  </ParamField>

  <ParamField path="password" type="password" placeholder="NewSecureP@ss123" required>
    New password
  </ParamField>

  <Expandable title="Headers" defaultOpen>
    <ParamField header="Content-Type" value="application/json" />
  </Expandable>

  <Button
    onClick={() => {
      alert('Sending request...');
    }}
  >
    Send Request
  </Button>
</Card>

## Process Flow

<Steps>
  <Step title="Token Validation">
    - Check token presence
    - Validate token format
    - Decrypt reset token
    - Extract email and OTP
  </Step>

  <Step title="Password Validation">
    - Validate password format
    - Check password requirements:
      - Minimum 8 characters
      - One uppercase letter
      - One lowercase letter
      - One number
      - One special character
  </Step>

  <Step title="OTP Verification">
    - Verify user exists
    - Check OTP matches stored value
    - Validate OTP hasn't expired
    - Verify correct parent public key (for child accounts)
  </Step>

  <Step title="Password Update">
    - Hash new password
    - Update user's password
    - Clear reset token and OTP
    - Record password change timestamp
  </Step>
</Steps>

## Response Examples

<ResponseField name="200: Success">
```json
{
  "success": true,
  "data": {
    "message": "Password updated successfully"
  }
}
```
</ResponseField>

<ResponseField name="400: Invalid Token">
```json
{
  "success": false,
  "error": {
    "code": "INVALID_TOKEN",
    "message": "Invalid or malformed reset token"
  }
}
```
</ResponseField>

<ResponseField name="400: Invalid Password">
```json
{
  "success": false,
  "error": {
    "code": "INVALID_PASSWORD_FORMAT",
    "message": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
  }
}
```
</ResponseField>

## Error Codes

<ResponseField name="400 Bad Request">
  - `TOKEN_NOT_PROVIDED` - Reset token required
  - `INVALID_TOKEN` - Invalid/malformed token
  - `INVALID_PASSWORD_FORMAT` - Password requirements not met
  - `MISSING_PARENT_PUBLIC_KEY` - Parent public key required for child accounts
  - `INVALID_PARENT_PUBLIC_KEY_FORMAT` - Invalid public key format
</ResponseField>

<ResponseField name="401 Unauthorized">
  - `INVALID_PARENT_PUBLIC_KEY` - Invalid parent public key
</ResponseField>

<ResponseField name="403 Forbidden">
  - `OTP_EXPIRED` - Reset code has expired
  - `INVALID_OTP` - Invalid reset code
</ResponseField>

<ResponseField name="404 Not Found">
  - `USER_NOT_FOUND` - No account found for token
</ResponseField>

## Password Requirements

<Card title="Password Rules" icon="key">
  - Minimum 8 characters
  - At least one uppercase letter
  - At least one lowercase letter
  - At least one number
  - At least one special character
  - No common patterns or sequences
</Card>

## Token Security

<Card title="Token Rules" icon="shield-check">
  - One-time use only
  - 15-minute expiration
  - Contains encrypted:
    - User email
    - OTP code
    - Parent public key (for child accounts)
</Card>