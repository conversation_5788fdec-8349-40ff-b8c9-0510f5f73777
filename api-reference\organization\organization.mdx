---
title: 'Update Organization'
description: 'Update organization details and authentication URLs for white-labeling'
api: 'POST https://api.authiqa.com/auth/update-organization'
---



## Overview

This endpoint allows parent account holders to customize their organization details and authentication URLs for white-labeling. The endpoint supports three modes of operation:
1. Initial Setup - Set organization name and URL
2. Auth URLs Update - Modify specific authentication URLs
3. Domain Restriction Toggle - Control domain validation for the widget

## API Details

### Request Format
<ParamField header="Authorization" type="string" required placeholder="Bearer YOUR_JWT_TOKEN">
    JWT token for authentication
</ParamField> 

#### Mode 1: Initial Setup
<ParamField body="organizationName" type="string" required>
  Organization name (must start with capital letter, min 4 chars)
</ParamField>

<ParamField body="organizationUrl" type="string" required>
  Base URL for organization (must use HTTPS)
</ParamField>

#### Mode 2: Auth URLs Update
<ParamField body="authUrls" type="object">
  Optional authentication URLs configuration. Only specified URLs will be updated.
</ParamField>

#### Mode 3: Domain Restriction Toggle
<ParamField body="domainRestrictionEnabled" type="boolean">
  Controls whether the authentication widget is restricted to the organization's domain.
  When `true` (default): Widget only works on the organization's domain.
  When `false`: Widget works on any domain (for testing purposes).
</ParamField>

### Default Auth URLs

When setting up the organization (Mode 1), the system automatically generates default auth URLs based on your organization URL:

```json
{
  "signup": "${organizationUrl}/signup.html",
  "signin": "${organizationUrl}/signin.html",
  "verify": "${organizationUrl}/verify.html",
  "reset": "${organizationUrl}/reset.html",
  "update": "${organizationUrl}/update.html",
  "resend": "${organizationUrl}/resend.html",
  "successful": "${organizationUrl}/successful.html"
}
```

<RequestExample>
```bash cURL
curl -X POST https://api.authiqa.com/auth/update-organization \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "organizationName": "Example Organization",
    "organizationUrl": "https://example.com"
  }'
```

```javascript JavaScript
// Mode 1: Initial Setup
const response = await fetch('https://api.authiqa.com/auth/update-organization', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    organizationName: 'Example Organization',
    organizationUrl: 'https://example.com'
  })
});

// Mode 2: Update specific auth URLs
const response = await fetch('https://api.authiqa.com/auth/update-organization', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    authUrls: {
      signin: 'https://auth.example.com/signin',
      signup: 'https://auth.example.com/signup'
    }
  })
});

// Mode 3: Toggle domain restriction
const response = await fetch('https://api.authiqa.com/auth/update-organization', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    domainRestrictionEnabled: false
  })
});
```

```python Python
# Mode 1: Initial Setup
response = requests.post(
    'https://api.authiqa.com/auth/update-organization',
    headers={
        'Authorization': 'Bearer YOUR_JWT_TOKEN',
        'Content-Type': 'application/json'
    },
    json={
        'organizationName': 'Example Organization',
        'organizationUrl': 'https://example.com'
    }
)

# Mode 2: Update specific auth URLs
response = requests.post(
    'https://api.authiqa.com/auth/update-organization',
    headers={
        'Authorization': 'Bearer YOUR_JWT_TOKEN',
        'Content-Type': 'application/json'
    },
    json={
        'authUrls': {
            'signin': 'https://auth.example.com/signin',
            'signup': 'https://auth.example.com/signup'
        }
    }
)
```
</RequestExample>

## Try It Out

<Note>
Select a mode and test the API:
</Note>

<Card>
  <Tabs>
    <Tab title="Mode 1: Initial Setup">
      <ParamField header="Authorization" required placeholder="Bearer YOUR_JWT_TOKEN">
        JWT token for authentication
      </ParamField>

      <ParamField body="organizationName" required placeholder="Example Organization">
        Organization name (must start with capital letter, min 4 chars)
      </ParamField>

      <ParamField body="organizationUrl" required placeholder="https://example.com">
        Base URL for organization (must use HTTPS)
      </ParamField>
    </Tab>

    <Tab title="Mode 2: Auth URLs">
      <ParamField header="Authorization" required placeholder="Bearer YOUR_JWT_TOKEN">
        JWT token for authentication
      </ParamField>

      <Expandable title="Authentication URLs" defaultOpen>
        <ParamField body="authUrls.signup" placeholder="https://auth.example.com/signup">
          Custom signup URL
        </ParamField>

        <ParamField body="authUrls.signin" placeholder="https://auth.example.com/signin">
          Custom signin URL
        </ParamField>

        <ParamField body="authUrls.verify" placeholder="https://auth.example.com/verify">
          Custom verification URL
        </ParamField>

        <ParamField body="authUrls.reset" placeholder="https://auth.example.com/reset">
          Custom password reset URL
        </ParamField>

        <ParamField body="authUrls.update" placeholder="https://auth.example.com/update">
          Custom password update URL
        </ParamField>

        <ParamField body="authUrls.resend" placeholder="https://auth.example.com/resend">
          Custom resend verification URL
        </ParamField>

        <ParamField body="authUrls.successful" placeholder="https://app.example.com/dashboard">
          Custom success redirect URL
        </ParamField>
      </Expandable>
    </Tab>

    <Tab title="Mode 3: Domain Restriction">
      <ParamField header="Authorization" required placeholder="Bearer YOUR_JWT_TOKEN">
        JWT token for authentication
      </ParamField>

      <ParamField body="domainRestrictionEnabled" type="boolean" default="true">
        Toggle domain restriction for the authentication widget
      </ParamField>
    </Tab>
  </Tabs>

  <Expandable title="Headers" defaultOpen>
    <ParamField header="Content-Type" value="application/json" />
  </Expandable>

  <Button
    onClick={() => {
      alert('Sending request...');
    }}
  >
    Send Request
  </Button>
</Card>

## Response Examples

### Mode 1: Initial Setup Response
<ResponseField name="200: Success">
```json
{
  "success": true,
  "data": {
    "message": "Organization details updated successfully",
    "data": {
      "organizationName": "Example Organization",
      "organizationUrl": "https://example.com",
      "authUrls": {
        "signup": "https://example.com/signup.html",
        "signin": "https://example.com/signin.html",
        "verify": "https://example.com/verify.html",
        "reset": "https://example.com/reset.html",
        "update": "https://example.com/update.html",
        "resend": "https://example.com/resend.html",
        "successful": "https://example.com/successful.html"
      }
    }
  }
}
```
</ResponseField>

### Mode 2: Auth URLs Update Response
<ResponseField name="200: Success">
```json
{
  "success": true,
  "data": {
    "message": "Auth URLs updated successfully",
    "data": {
      "authUrls": {
        "signin": "https://auth.example.com/signin",
        "signup": "https://auth.example.com/signup"
      }
    }
  }
}
```
</ResponseField>

### Mode 3: Domain Restriction Toggle Response
<ResponseField name="200: Success">
```json
{
  "success": true,
  "data": {
    "message": "Domain restriction setting updated successfully",
    "data": {
      "domainRestrictionEnabled": false
    }
  }
}
```
</ResponseField>

## Error Codes

<ResponseField name="400 Bad Request">
  - `INVALID_ORGANIZATION_NAME` - Organization name requirements not met
  - `INVALID_ORGANIZATION_URL` - Invalid URL format
  - `INVALID_AUTH_URLS` - Authentication URLs invalid/inaccessible
  - `MISSING_REQUIRED_URLS` - Not all required URLs provided
</ResponseField>

<ResponseField name="401 Unauthorized">
  - `UNAUTHORIZED` - Invalid/missing JWT token
  - `UNAUTHORIZED_ACCESS` - Child accounts cannot update organization
</ResponseField>

## Validation Rules

<CardGroup cols={2}>
  <Card title="Organization Name" icon="building">
    - Minimum 4 characters
    - Must start with capital letter
    - Cannot be null or empty
  </Card>
  <Card title="URLs" icon="link">
    - Valid URL format
    - Must use HTTPS
    - Auth URLs must be under organization domain
    - Organization URL must be set before updating auth URLs
  </Card>
</CardGroup>

## Domain Restriction Feature

<Card title="Domain Restriction Toggle" icon="shield-alt">
The domain restriction toggle controls where your authentication widget can be used:

- **Enabled (Default)**: Widget only works on your organization's domain, providing security against unauthorized use
- **Disabled**: Widget works on any domain, useful for testing on platforms like JSFiddle or local development environments

This setting can be toggled independently without affecting other organization settings.
</Card>

## Important Notes

<Note>
- Organization details can be updated multiple times
- Auth URLs can be updated individually without affecting other URLs
- Default auth URLs are automatically generated during initial setup
- Auth URLs must match the organization's domain
- Domain restriction is enabled by default for security but can be disabled for testing purposes
</Note>
