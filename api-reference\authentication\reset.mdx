---
title: 'Reset Password'
description: 'Request password reset for parent and child accounts'
api: 'POST https://api.authiqa.com/auth/reset-password'
---


## Overview

This endpoint initiates a password reset flow by sending a reset link to the user's verified email address. Supports both parent and child accounts with appropriate branding.

## API Details

### Request Format

<ParamField body="email" type="string" required>
  Email address for the account
</ParamField>

<ParamField body="parentPublicKey" type="string">
  Required only for child accounts
</ParamField>

<RequestExample>
```bash cURL
curl -X POST https://api.authiqa.com/auth/reset-password \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

```javascript JavaScript
const response = await fetch('https://api.authiqa.com/auth/reset-password', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>'
  })
});
```

```python Python
import requests

response = requests.post(
    'https://api.authiqa.com/auth/reset-password',
    json={
        'email': '<EMAIL>'
    }
)
```

```php PHP
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.authiqa.com/auth/reset-password');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'email' => '<EMAIL>'
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);
```
</RequestExample>

## Try It Out

<Note>
Test the API by providing an email address:
</Note>

<Card>
  <ParamField path="email" placeholder="<EMAIL>" required>
    Email address to receive reset link
  </ParamField>

  <ParamField path="parentPublicKey" placeholder="APK_1234567890abcdef_1234">
    Parent public Key (required for child accounts)
  </ParamField>

  <Expandable title="Headers" defaultOpen>
    <ParamField header="Content-Type" value="application/json" />
  </Expandable>

  <Button
    onClick={() => {
      alert('Sending request...');
    }}
  >
    Send Request
  </Button>
</Card>

## Process Flow

<Steps>
  <Step title="Request Validation">
    - Validate email format
    - Check parent public key (if provided)
    - Determine account type
    - Verify email confirmation
    - Check rate limiting
  </Step>

  <Step title="Account Validation">
    For Child Accounts:
    - Validate parent public key
    - Verify parent-child relationship
    - Check parent account status

    For Parent Accounts:
    - Ensure no parent public key provided
    - Verify ROOT status
  </Step>

  <Step title="Token Generation">
    - Generate secure OTP
    - Create encrypted reset token with:
      - Email
      - OTP
      - Parent public key (for child accounts)
    - Build reset link
  </Step>

  <Step title="Email Delivery">
    Parent Accounts:
    - Authiqa-branded email
    - Standard reset template

    Child Accounts:
    - Parent organization branding
    - Organization-specific template
  </Step>
</Steps>

## Response Examples

<ResponseField name="200: Success">
```json
{
  "success": true,
  "data": {
    "message": "Password reset link has been sent to your email"
  }
}
```
</ResponseField>

<ResponseField name="400: Invalid Request">
```json
{
  "success": false,
  "error": {
    "code": "INVALID_EMAIL_FORMAT",
    "message": "Invalid email format"
  }
}
```
</ResponseField>

<ResponseField name="429: Rate Limited">
```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Please wait before requesting another reset link"
  }
}
```
</ResponseField>

## Error Codes

### Parent Account Errors
<ResponseField name="400 Bad Request">
  - `INVALID_REQUEST` - Parent public key provided for parent account
</ResponseField>

### Child Account Errors
<ResponseField name="400 Bad Request">
  - `MISSING_PARENT_PUBLIC_KEY` - Parent public key required
  - `INVALID_PARENT_PUBLIC_KEY_FORMAT` - Invalid public key format
</ResponseField>

<ResponseField name="401 Unauthorized">
  - `INVALID_PARENT_PUBLICI_KEY` - Invalid parent public key
</ResponseField>

### General Errors
<ResponseField name="400 Bad Request">
  - `MISSING_REQUEST_BODY` - Request body required
  - `INVALID_REQUEST_BODY` - Invalid JSON format
  - `MISSING_REQUIRED_FIELDS` - Email field required
  - `INVALID_EMAIL_FORMAT` - Invalid email format
</ResponseField>

<ResponseField name="403 Forbidden">
  - `EMAIL_NOT_VERIFIED` - Email verification required
  - `ACCOUNT_INACTIVE` - Account not active
  - `ACCOUNT_LOCKED` - Account is locked
</ResponseField>

<ResponseField name="404 Not Found">
  - `USER_NOT_FOUND` - No account with provided email
</ResponseField>

<ResponseField name="429 Too Many Requests">
  - `RATE_LIMIT_EXCEEDED` - Wait before requesting again
</ResponseField>

## Email Templates

<CardGroup cols={2}>
  <Card title="Parent Account Email" icon="building">
    - Authiqa branding
    - Subject: "Reset Your Password - Authiqa"
    - 15-minute expiry notice
  </Card>
  <Card title="Child Account Email" icon="users">
    - Parent organization branding
    - Subject: "Reset Your Password - [Organization]"
    - 15-minute expiry notice
  </Card>
</CardGroup>

## Rate Limiting

<Card title="Limits" icon="shield">
  - 1 request per minute per email
  - Token expires after 15 minutes
  - Account locks after 5 failed reset attempts
</Card>