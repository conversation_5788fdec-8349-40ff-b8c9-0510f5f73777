---
title: 'Quickstart'
description: 'Get started with Authiqa in minutes'
---

This guide will help you integrate Authiqa authentication into your application.

## Prerequisites
- An Authiqa account
- Your public key
- A web application where you want to add authentication

## Integration Steps

<Steps>
  <Step title="Add Container and Widget Script">
    ```html
    <!-- Add the container div -->
    <div id="authiqa"></div>

    <!-- Add the widget script -->
    <script 
src="https://widget.authiqa.com"
        defer
        data-public-key="YOUR_PUBLIC_KEY"
        action="signin"
    ></script>
    ```
    The `action` attribute can be: `signin`, `signup`, `reset`, `update`, `verify`, or `resend`
  </Step>


  <Step title="Configure Theme (Optional)">
    ```html
    <!-- Dark theme -->
    <script 
        defer
        src="https://widget.authiqa.com"
        data-public-key="YOUR_PUBLIC_KEY"
        action="signin"
        theme="dark"
    ></script>

    <!-- Custom styling -->
    <script 
        defer
        src="https://widget.authiqa.com"
        data-public-key="YOUR_PUBLIC_KEY"
        action="signin"
        disable-styles="true"
    ></script>
    ```
  </Step>

  <Step title="Handle Authentication Events">
    ```javascript
    // Success events
    widget.on('widget:success', (data) => {
      console.log('Authentication successful:', data);
      // Tokens are automatically stored in localStorage:
      // - authiqa_token: JWT access token
      // - authiqa_token_expiration: Token expiration
      // - authiqa_parent_jwt_secret: Parent JWT secret
    });

    // Error events  
    widget.on('widget:error', (error) => {
      console.error('Authentication failed:', error);
    });

    // Verification events
    widget.on('verify:success', () => {
      console.log('Email verification successful');
    });
    ```
  </Step>
</Steps>

## Widget Events

The widget emits several events you can listen for:
- `auth:success` - Authentication successful
- `auth:error` - Authentication failed
- `verify:success` - Email verification successful
- `verify:error` - Email verification failed

## Verification

Test your integration by:
1. Opening your application
2. Clicking the login button
3. Completing the authentication flow

<Card title="Need detailed setup?" icon="wrench" href="/authentication/widget-setup">
  Check our complete widget setup guide
</Card>

## Next Steps

<CardGroup cols={2}>
  <Card
    title="Configure Widget"
    icon="gear"
    href="/authentication/configuration"
  >
    Customize the widget appearance and behavior
  </Card>
  <Card
    title="Verify JWTs"
    icon="shield-check"
    href="/features/security"
  >
    Learn how to verify authentication tokens
  </Card>
</CardGroup>